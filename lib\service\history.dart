import 'dart:async';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:get_it/get_it.dart';
import 'package:drift/drift.dart';

import '../utils/crypto_utils.dart';

class HistoryService {
  final StorageService storage;

  HistoryService({required this.storage});

  static Future<void> register() async {
    final service = HistoryService(storage: GetIt.I.get<StorageService>());
    GetIt.I.registerSingleton<HistoryService>(service);
  }

  Future<History?> getHistoryByUniqueKey(String uniqueKey) async {
    return storage.getHistoryByUniqueKey(uniqueKey);
  }

  Future<List<History>> getAllHistories() async {
    return await storage.getHistories();
  }

  Future<void> clearAllHistories() async {
    await storage.clearAllHistories();
  }

  Future<void> deleteHistory(int id) async {
    await storage.deleteHistory(id);
  }

  /// 开始记录播放历史
  Future<void> addHistory({
    required String videoPath,
    required String url,
    required String headers,
    required Duration duration,
  }) async {
    // 生成唯一键
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);

    // 检查是否已存在历史记录
    final existingHistory = await getHistoryByUniqueKey(uniqueKey);

    if (existingHistory == null) {
      // 创建新的历史记录
      final history = History(
        id: 0, // 自动生成
        uniqueKey: uniqueKey,
        duration: duration.inMilliseconds, // 初始为0，后续更新
        position: 0,
        url: url,
        headers: headers,
        updateTime: DateTime.now().millisecondsSinceEpoch,
        danmakuUpdateTime: 0,
      );
      await storage.createHistory(history);
    } else {
      // 更新历史记录
      final companion = HistoriesCompanion(
        id: Value(existingHistory.id),
        headers: Value(headers),
        duration: Value(duration.inMilliseconds),
        updateTime: Value(DateTime.now().millisecondsSinceEpoch),
      );
      await storage.updateHistory(companion);
    }
  }

  /// 更新播放进度
  Future<void> updateProgress({
    required Duration position,
    required Duration duration,
    required int id,
  }) async {
    // 使用 partial update 来提高性能
    final companion = HistoriesCompanion(
      id: Value(id),
      position: Value(position.inMilliseconds),
      duration: Value(duration.inMilliseconds),
      updateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );

    await storage.updateProgress(companion);
  }

  /// 保存弹幕路径
  Future<void> saveDanmakuUpdateTime({required String videoPath}) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    final companion = HistoriesCompanion(
      danmakuUpdateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );
    await (storage.update(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).write(companion);
  }

  /// 获取播放历史
  Future<History?> getPlaybackHistory(String videoPath) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    return await getHistoryByUniqueKey(uniqueKey);
  }

  /// 获取播放进度百分比 TODO
  double getProgressPercentage(String videoPath) {
    // 这里可以从缓存中获取，避免频繁数据库查询
    // 简化实现，实际使用时可以优化
    return 0.0;
  }

  /// 判断视频是否已观看完成 TODO
  bool isVideoCompleted(String videoPath) {
    // 简化实现，可以根据播放进度判断
    return false;
  }
}
