import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/crypto_utils.dart';
import 'package:get_it/get_it.dart';
import 'package:signals/signals_flutter.dart';
import 'package:webdav_client/webdav_client.dart';

abstract class FileExplorerProvider {
  Future<List<FileItem>> listFiles(String path);
}

class FileExplorerService {
  final FileExplorerProvider provider;
  final HistoryService historyService = GetIt.I.get<HistoryService>();
  final path = signal('/');

  late FutureSignal<List<FileItem>> files;
  final Signal<String?> error = signal(null);

  FileExplorerService({required this.provider}) {
    files = futureSignal(
      () async => provider.listFiles(path.value),
      dependencies: [path],
    );
  }

  void goDir(String name) {
    path.value = '${path.value}/$name';
  }

  bool goBack() {
    if (path.value == '/') {
      return false;
    }
    path.value = path.value
        .split('/')
        .sublist(0, path.value.split('/').length - 1)
        .join('/');
    return true;
  }
}

// WebDAV implementation (placeholder)
class WebDAVFileExplorerProvider implements FileExplorerProvider {
  late Client client;
  WebDAVFileExplorerProvider(MediaLibrary mediaLibrary) {
    if (mediaLibrary.isAnonymous) {
      client = newClient(mediaLibrary.url);
    } else {
      client = newClient(
        mediaLibrary.url,
        user: mediaLibrary.account!,
        password: mediaLibrary.password!,
      );
    }
  }

  @override
  Future<List<FileItem>> listFiles(String path) async {
    List<FileItem> list = [];
    var fileList = await client.readDir(path);
    for (var file in fileList) {
      if (file.name == null || file.path == null) {
        continue;
      }
      if (FileItem.getFileType(file.name!) != FileType.video && !file.isDir!) {
        continue;
      }
      if (file.isDir!) {
        list.add(
          FileItem(name: file.name!, path: file.path!, type: FileType.folder),
        );
        continue;
      }
      var uniqueKey = CryptoUtils.generateVideoUniqueKey(file.path!);
      var history = await GetIt.I.get<HistoryService>().getHistoryByUniqueKey(
        uniqueKey,
      );
      list.add(
        FileItem(
          name: file.name!,
          path: file.path!,
          type: FileItem.getFileType(file.name!),
          size: file.size,
          uniqueKey: uniqueKey,
          history: history,
        ),
      );
    }
    return list;
  }
}

// Local file system implementation (placeholder)
class LocalFileExplorerProvider implements FileExplorerProvider {
  @override
  Future<List<FileItem>> listFiles(String path) async {
    // TODO: Implement local file system listing
    return [
      FileItem(
        name: 'Local Folder',
        path: '$path/Local Folder',
        type: FileType.folder,
      ),
      FileItem(
        name: 'local_video.mp4',
        path: '$path/local_video.mp4',
        type: FileType.video,
        size: 1024 * 1024 * 200, // 200MB
      ),
    ];
  }
}
