import 'dart:convert';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';

final uuid = Uuid();

extension MediaLibraryExtension on MediaLibrary {
  static MediaLibrary create() {
    return MediaLibrary(
      id: -1,
      name: '',
      url: '',
      headers: '{}',
      mediaType: MediaType.webdav,
      isAnonymous: false,
    );
  }
}

class MediaLibraryService {
  StorageService storage;

  MediaLibraryService({required this.storage});
  // TODO 改为Future
  final Signal<List<MediaLibrary>> mediaLibraries = signal([]);

  static Future<void> register() async {
    var service = MediaLibraryService(storage: GetIt.I.get<StorageService>());
    await service.init();
    GetIt.I.registerSingleton<MediaLibraryService>(service);
  }

  Future<void> init() async {
    mediaLibraries.value = await storage.getMediaLibraries();
  }

  Future<MediaLibrary?> getMediaLibrary(int id) async {
    return await storage.getMediaLibrary(id);
  }

  Future<void> updateMediaLibrary(MediaLibrary mediaLibrary) async {
    if (mediaLibrary.mediaType == MediaType.webdav) {
      mediaLibrary = mediaLibrary.copyWith(
        headers: jsonEncode({
          "Authorization":
              "Basic ${base64Encode(utf8.encode('${mediaLibrary.account!}:${mediaLibrary.password!}'))}",
        }),
      );
    }
    if (mediaLibrary.id == -1) {
      await storage.createMediaLibrary(mediaLibrary);
    } else {
      await storage.updateMediaLibrary(mediaLibrary);
    }
    init();
  }

  Future<void> deleteMediaLibrary(int id) async {
    await storage.deleteMediaLibrary(id);
    init();
  }
}
