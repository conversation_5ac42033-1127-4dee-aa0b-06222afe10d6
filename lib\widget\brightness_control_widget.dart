import 'package:flutter/material.dart';
import 'package:screen_brightness/screen_brightness.dart';

/// 亮度控制组件
/// 显示当前屏幕亮度并提供调节功能
class BrightnessControlWidget extends StatefulWidget {
  final double brightness;
  final bool isVisible;
  final Duration visibilityDuration;

  const BrightnessControlWidget({
    super.key,
    required this.brightness,
    required this.isVisible,
    this.visibilityDuration = const Duration(seconds: 2),
  });

  @override
  State<BrightnessControlWidget> createState() =>
      _BrightnessControlWidgetState();
}

class _BrightnessControlWidgetState extends State<BrightnessControlWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(BrightnessControlWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: _buildBrightnessIndicator(),
        );
      },
    );
  }

  Widget _buildBrightnessIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getBrightnessIcon(), color: Colors.white, size: 32),
          const SizedBox(height: 8),
          Text(
            '${(widget.brightness * 100).round()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildBrightnessBar(),
        ],
      ),
    );
  }

  Widget _buildBrightnessBar() {
    return Container(
      width: 4,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          width: 4,
          height: 100 * widget.brightness,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }

  IconData _getBrightnessIcon() {
    if (widget.brightness < 0.3) {
      return Icons.brightness_low;
    } else if (widget.brightness < 0.7) {
      return Icons.brightness_medium;
    } else {
      return Icons.brightness_high;
    }
  }
}

/// 亮度控制服务
class BrightnessControlService {
  static double _currentBrightness = 0.5;
  static double _systemBrightness = 0.5;
  static final List<VoidCallback> _listeners = [];

  /// 获取当前亮度
  static double get currentBrightness => _currentBrightness;

  /// 设置亮度
  static Future<void> setBrightness(double brightness) async {
    brightness = brightness.clamp(0.0, 1.0);
    _currentBrightness = brightness;

    try {
      await ScreenBrightness().setApplicationScreenBrightness(brightness);
      _notifyListeners();
    } catch (e) {
      debugPrint('设置亮度失败: $e');
    }
  }

  /// 同步获取当前亮度值
  static Future<double> getBrightness() async {
    try {
      _currentBrightness = await ScreenBrightness().application;
      return _currentBrightness;
    } catch (e) {
      debugPrint('获取亮度失败: $e');
      return _currentBrightness;
    }
  }

  /// 增加亮度
  static Future<void> increaseBrightness([double delta = 0.1]) async {
    await setBrightness(_currentBrightness + delta);
  }

  /// 减少亮度
  static Future<void> decreaseBrightness([double delta = 0.1]) async {
    await setBrightness(_currentBrightness - delta);
  }

  /// 重置为系统亮度
  static Future<void> resetToSystemBrightness() async {
    try {
      await ScreenBrightness().resetApplicationScreenBrightness();
      _currentBrightness = _systemBrightness;
      _notifyListeners();
    } catch (e) {
      debugPrint('重置亮度失败: $e');
    }
  }

  /// 初始化亮度服务
  static Future<void> initialize() async {
    try {
      _systemBrightness = await ScreenBrightness().system;
      _currentBrightness = await ScreenBrightness().application;
    } catch (e) {
      debugPrint('初始化亮度服务失败: $e');
      _systemBrightness = 0.5;
      _currentBrightness = 0.5;
    }
  }

  /// 添加监听器
  static void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 移除监听器
  static void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 通知监听器
  static void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// 释放资源
  static void dispose() {
    _listeners.clear();
  }
}
