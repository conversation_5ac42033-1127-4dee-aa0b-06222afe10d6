import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:dandanplay_flutter/service/player/danmaku.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:dandanplay_flutter/widget/player_notification_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals/signals.dart';
import 'package:video_player/video_player.dart';
import 'player_state.dart';
import 'timer_manager.dart';
import '../history.dart';

/// 节流信号更新器
/// 用于控制信号更新频率，减少不必要的UI重建
class ThrottledSignalUpdater {
  Timer? _throttleTimer;
  final Duration _interval;

  ThrottledSignalUpdater({Duration? interval})
    : _interval = interval ?? const Duration(milliseconds: 66); // 默认15fps

  /// 节流更新方法
  /// 如果在节流间隔内，则跳过更新
  void updateWithThrottle(VoidCallback updateFn) {
    if (_throttleTimer?.isActive != true) {
      updateFn();
      _throttleTimer = Timer(_interval, () {});
    }
  }

  /// 立即更新（绕过节流）
  /// 用于关键状态变化
  void updateImmediately(VoidCallback updateFn) {
    updateFn();
  }

  /// 释放资源
  void dispose() {
    _throttleTimer?.cancel();
    _throttleTimer = null;
  }
}

/// 进度条状态复合类
/// 将位置、时长和缓冲位置合并为单一状态，减少UI重建
class ProgressBarState {
  final Duration position;
  final Duration duration;
  final Duration bufferedPosition;

  const ProgressBarState({
    required this.position,
    required this.duration,
    required this.bufferedPosition,
  });

  /// 播放进度百分比 (0.0 - 1.0)
  double get progress {
    if (duration.inMilliseconds <= 0) return 0.0;
    return (position.inMilliseconds / duration.inMilliseconds).clamp(0.0, 1.0);
  }

  /// 缓冲进度百分比 (0.0 - 1.0)
  double get bufferedProgress {
    if (duration.inMilliseconds <= 0) return 0.0;
    return (bufferedPosition.inMilliseconds / duration.inMilliseconds).clamp(
      0.0,
      1.0,
    );
  }

  /// 是否有有效的时长信息
  bool get hasValidDuration => duration.inMilliseconds > 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProgressBarState &&
          runtimeType == other.runtimeType &&
          position == other.position &&
          duration == other.duration &&
          bufferedPosition == other.bufferedPosition;

  @override
  int get hashCode =>
      position.hashCode ^ duration.hashCode ^ bufferedPosition.hashCode;

  @override
  String toString() =>
      'ProgressBarState(position: $position, duration: $duration, buffered: $bufferedPosition)';
}

/// 视频播放服务
/// 封装VideoPlayerController，提供状态管理和播放控制
class VideoPlayerService {
  VideoPlayerController? _controller;
  final HistoryService _historyService = GetIt.I<HistoryService>();

  // 播放器状态信号
  final Signal<PlayerState> _playerState = Signal(PlayerState.idle);
  final Signal<Duration> _position = Signal(Duration.zero);
  final Signal<Duration> _duration = Signal(Duration.zero);
  final Signal<Duration> _targetPosition = Signal(Duration.zero);
  final Signal<Duration> _bufferedPosition = Signal(Duration.zero);
  final Signal<double> _playbackSpeed = Signal(1.0);
  final Signal<String?> _errorMessage = Signal(null);

  // 复合信号 - 进度条状态
  late final Computed<ProgressBarState> _progressBarState;

  // 当前视频信息
  String _currentVideoPath;
  GlobalKey repaintBoundaryKey;
  Timer? _positionTimer;
  final DanmakuService danmakuService = DanmakuService();
  late History _history;

  // 节流更新器
  late final ThrottledSignalUpdater _signalThrottler;

  // 定时器管理器
  late final PlayerTimerManager _timerManager;

  VideoPlayerService({
    required String videoPath,
    required this.repaintBoundaryKey,
  }) : _currentVideoPath = videoPath {
    // 初始化节流器，设置为15fps（66ms间隔）
    _signalThrottler = ThrottledSignalUpdater();

    // 初始化定时器管理器
    _timerManager = PlayerTimerManager();

    // 初始化复合信号 - 进度条状态
    _progressBarState = computed(
      () => ProgressBarState(
        position: _position.value,
        duration: _duration.value,
        bufferedPosition: _bufferedPosition.value,
      ),
    );
  }

  /// 播放器状态
  ReadonlySignal<PlayerState> get playerState => _playerState.readonly();

  /// 当前播放位置
  ReadonlySignal<Duration> get position => _position.readonly();

  /// 视频总长度
  ReadonlySignal<Duration> get duration => _duration.readonly();

  /// 目标播放位置（跳转目标）
  ReadonlySignal<Duration> get targetPosition => _targetPosition.readonly();

  /// 已缓冲位置
  ReadonlySignal<Duration> get bufferedPosition => _bufferedPosition.readonly();

  /// 播放速度
  ReadonlySignal<double> get playbackSpeed => _playbackSpeed.readonly();

  /// 错误信息
  ReadonlySignal<String?> get errorMessage => _errorMessage.readonly();

  /// 进度条状态（复合信号）
  ReadonlySignal<ProgressBarState> get progressBarState =>
      _progressBarState.readonly();

  /// 视频播放器控制器
  VideoPlayerController? get controller => _controller;

  /// 当前视频路径
  String get currentVideoPath => _currentVideoPath;

  /// 是否已初始化
  bool get isInitialized => _controller?.value.isInitialized ?? false;

  /// 初始化视频播放器 TODO 该为静态方法
  Future<void> initialize({
    required String videoPath,
    Map<String, String>? headers,
  }) async {
    try {
      _playerState.value = PlayerState.loading;
      _errorMessage.value = null;
      _currentVideoPath = videoPath;

      // 释放之前的控制器
      await _disposeController();

      // 创建新的控制器
      if (videoPath.startsWith('http://') || videoPath.startsWith('https://')) {
        // 网络视频
        _controller = VideoPlayerController.networkUrl(
          Uri.parse(videoPath),
          httpHeaders: headers ?? {},
        );
      } else {
        // 本地文件
        _controller = VideoPlayerController.file(File(videoPath));
      }

      // 添加监听器
      _controller!.addListener(_onVideoPlayerUpdate);

      // 初始化控制器
      await _controller!.initialize();

      // 添加历史记录
      await _historyService.addHistory(
        videoPath: videoPath,
        url: videoPath,
        headers: jsonEncode(headers ?? {}),
        duration: _duration.value,
      );
      _history =
          await _historyService.getPlaybackHistory(videoPath) ??
          History(
            id: 0,
            uniqueKey: '',
            duration: 0,
            position: 0,
            url: '',
            headers: '',
            updateTime: 0,
            danmakuUpdateTime: 0,
          );

      danmakuService.currentVideoPath = videoPath;
      danmakuService.history = _history;
      danmakuService.duration = _duration.value;

      // 恢复播放进度
      await _restoreProgress();
      // 更新状态
      //_playerState.value = PlayerState.ready;
      _duration.value = _controller!.value.duration;

      // 自动播放
      await play();

      // 加载弹幕
      danmakuService.loadDanmaku();

      // 开始定时更新播放历史
      _startPositionTimer();
    } catch (e) {
      _playerState.value = PlayerState.error;
      _errorMessage.value = e.toString();
      rethrow;
    }
  }

  /// 播放视频
  Future<void> play() async {
    if (_controller != null && _playerState.value.canPlay) {
      await _controller!.play();
      _playerState.value = PlayerState.playing;
      // 更新定时器管理器播放状态
      _timerManager.updatePlayingState(true);
    }
  }

  /// 暂停视频
  Future<void> pause() async {
    if (_controller != null && _playerState.value.canPause) {
      await _controller!.pause();
      _playerState.value = PlayerState.paused;
      // 更新定时器管理器播放状态
      _timerManager.updatePlayingState(false);
    }
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    if (_controller != null && _controller!.value.isInitialized) {
      _targetPosition.value = position;
      await _controller!.seekTo(position);
    }
  }

  /// 相对跳转
  void seekRelative(Duration offset) {
    final currentPosition = position.value;
    final newPosition = currentPosition + offset;
    seekTo(newPosition);

    // 显示跳转通知
    final offsetText =
        offset.isNegative
            ? '-${formatDuration(-offset)}'
            : '+${formatDuration(offset)}';
    PlayerNotificationManager.showInfo(
      '跳转 $offsetText',
      duration: const Duration(seconds: 2),
    );
  }

  /// 设置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    if (_controller != null && _controller!.value.isInitialized) {
      await _controller!.setPlaybackSpeed(speed);
      _playbackSpeed.value = speed;
    }
  }

  Future<void> doubleSpeed(bool isDouble) async {
    final currentSpeed = _playbackSpeed.value;
    final newSpeed = currentSpeed * (isDouble ? 2 : 0.5);
    await setPlaybackSpeed(newSpeed);
  }

  /// 切换播放/暂停
  Future<void> togglePlayPause() async {
    if (_playerState.value.isPlaying) {
      await pause();
      danmakuService.syncWithVideo(false);
    } else if (_playerState.value.canPlay) {
      await play();
      danmakuService.syncWithVideo(true);
    }
  }

  /// 视频播放器状态更新回调
  /// 使用节流机制优化更新频率，关键状态变化立即更新
  void _onVideoPlayerUpdate() {
    if (_controller == null) return;

    final value = _controller!.value;
    final currentState = _playerState.value;

    // 检查是否有关键状态变化（需要立即更新）
    final hasError = value.hasError;
    final isBuffering = value.isBuffering;
    final isPlaying = value.isPlaying;
    final isInitialized = value.isInitialized;

    PlayerState newState = currentState;
    if (hasError) {
      newState = PlayerState.error;
    } else if (isBuffering) {
      newState = PlayerState.buffering;
    } else if (isPlaying) {
      newState = PlayerState.playing;
    } else if (isInitialized) {
      newState = PlayerState.paused;
    }

    // 关键状态变化立即更新
    if (newState != currentState || hasError) {
      _signalThrottler.updateImmediately(() {
        _playerState.value = newState;
        if (hasError) {
          _errorMessage.value = value.errorDescription;
        }
      });
    }

    // 位置和缓冲信息使用节流更新
    _signalThrottler.updateWithThrottle(() {
      // 更新播放位置
      _position.value = value.position;

      // 更新缓冲位置
      if (value.buffered.isNotEmpty) {
        _bufferedPosition.value = value.buffered.last.end;
      }
    });
  }

  /// 更新播放历史记录
  Future<void> updatePlaybackHistory() async {
    await _historyService.updateProgress(
      id: _history.id,
      position: _position.value,
      duration: _duration.value,
    );
  }

  /// 保存视频快照
  ///
  /// [repaintBoundaryKey] The global key of the repaint boundary to capture.
  /// Returns `true` if the snapshot was saved successfully, `false` otherwise.
  Future<bool> saveSnapshot() async {
    try {
      final boundary =
          repaintBoundaryKey.currentContext?.findRenderObject()
              as RenderRepaintBoundary?;
      if (boundary == null) {
        debugPrint('Cannot find render object');
        return false;
      }

      if (_history.uniqueKey.isEmpty) {
        debugPrint('Cannot get video unique key');
        return false;
      }

      final image = await boundary.toImage(
        pixelRatio: 3.0,
      ); // Use a fixed pixel ratio for potentially higher quality
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List();

      if (pngBytes != null) {
        final directory = await getApplicationDocumentsDirectory();
        final snapshotDir = Directory('${directory.path}/screenshots');
        if (!await snapshotDir.exists()) {
          await snapshotDir.create(recursive: true);
        }
        final snapshotFile = File(
          '${snapshotDir.path}/${_history.uniqueKey}.png',
        );
        await snapshotFile.writeAsBytes(pngBytes);
        debugPrint('Snapshot saved to ${snapshotFile.path}');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error saving snapshot: $e');
      return false;
    }
  }

  /// 启动定时器任务
  /// 使用PlayerTimerManager统一管理定时器任务
  void _startPositionTimer() {
    // 取消旧的定时器（向后兼容）
    _positionTimer?.cancel();
    _positionTimer = null;

    // 添加历史记录更新任务（高优先级）
    _timerManager.scheduleTask(
      TimerTask(
        type: TimerTaskType.historyUpdate,
        name: '历史记录更新',
        execute: () async {
          await updatePlaybackHistory();
        },
      ),
    );

    // 添加快照保存任务（低优先级）
    _timerManager.scheduleTask(
      TimerTask(
        type: TimerTaskType.snapshotSave,
        name: '视频快照保存',
        execute: () async {
          await saveSnapshot();
        },
      ),
    );

    // 设置初始播放状态
    _timerManager.updatePlayingState(_playerState.value.isPlaying);
  }

  /// 恢复播放进度
  Future<Duration> restoreProgress() async {
    if (_history.position > 0) {
      final position = Duration(milliseconds: _history.position);
      await seekTo(position);
      return position;
    }
    return Duration.zero;
  }

  Future<void> _restoreProgress() async {
    try {
      if (_history.position > 0) {
        // 显示恢复播放通知
        final position = Duration(milliseconds: _history.position);
        await seekTo(position);
        final positionText = formatDuration(position);
        PlayerNotificationManager.showInfo(
          '已恢复到 $positionText',
          duration: const Duration(seconds: 3),
        );

        debugPrint('恢复播放历史: $positionText');
      }
    } catch (e) {
      debugPrint('恢复播放历史失败: $e');
    }
  }

  /// 释放控制器
  Future<void> _disposeController() async {
    // 停止定时器管理器的所有任务
    _timerManager.dispose();

    // 取消旧的定时器（向后兼容）
    _positionTimer?.cancel();
    _positionTimer = null;

    if (_controller != null) {
      _controller!.removeListener(_onVideoPlayerUpdate);
      await _controller!.dispose();
      _controller = null;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await _disposeController();

    // 释放定时器管理器
    _timerManager.dispose();

    // 释放节流器
    _signalThrottler.dispose();

    // 释放复合信号
    _progressBarState.dispose();

    // 释放信号
    _playerState.dispose();
    _position.dispose();
    _duration.dispose();
    _targetPosition.dispose();
    _bufferedPosition.dispose();
    _playbackSpeed.dispose();
    _errorMessage.dispose();
    danmakuService.dispose();
  }
}
