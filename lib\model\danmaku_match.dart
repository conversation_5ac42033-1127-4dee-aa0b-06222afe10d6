/// 弹幕匹配状态枚举
enum DanmakuMatchStatus {
  /// 空闲状态
  idle,
  
  /// 正在下载视频数据
  downloadingVideo,
  
  /// 正在计算hash
  calculatingHash,
  
  /// 正在匹配弹幕
  matching,
  
  /// 匹配成功
  success,
  
  /// 匹配失败
  failed,
}

/// 弹幕匹配状态扩展
extension DanmakuMatchStatusExtension on DanmakuMatchStatus {
  /// 获取状态描述
  String get description {
    switch (this) {
      case DanmakuMatchStatus.idle:
        return '准备就绪';
      case DanmakuMatchStatus.downloadingVideo:
        return '正在下载视频数据...';
      case DanmakuMatchStatus.calculatingHash:
        return '正在计算文件哈希...';
      case DanmakuMatchStatus.matching:
        return '正在匹配弹幕...';
      case DanmakuMatchStatus.success:
        return '匹配成功';
      case DanmakuMatchStatus.failed:
        return '匹配失败';
    }
  }

  /// 是否为进行中状态
  bool get isInProgress {
    return this == DanmakuMatchStatus.downloadingVideo ||
           this == DanmakuMatchStatus.calculatingHash ||
           this == DanmakuMatchStatus.matching;
  }

  /// 是否为完成状态
  bool get isCompleted {
    return this == DanmakuMatchStatus.success ||
           this == DanmakuMatchStatus.failed;
  }
}

/// 弹幕匹配结果信息
class DanmakuMatchResult {
  /// 匹配到的剧集名称
  final String? episodeName;
  
  /// 匹配置信度 (0.0 - 1.0)
  final double? confidence;
  
  /// 弹幕数量
  final int danmakuCount;
  
  /// 匹配耗时
  final Duration matchDuration;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 是否为网络视频
  final bool isNetworkVideo;
  
  /// 文件大小（字节）
  final int? fileSize;
  
  /// 文件hash
  final String? fileHash;

  const DanmakuMatchResult({
    this.episodeName,
    this.confidence,
    required this.danmakuCount,
    required this.matchDuration,
    this.errorMessage,
    required this.isNetworkVideo,
    this.fileSize,
    this.fileHash,
  });

  /// 创建成功结果
  factory DanmakuMatchResult.success({
    required String episodeName,
    required double confidence,
    required int danmakuCount,
    required Duration matchDuration,
    required bool isNetworkVideo,
    int? fileSize,
    String? fileHash,
  }) {
    return DanmakuMatchResult(
      episodeName: episodeName,
      confidence: confidence,
      danmakuCount: danmakuCount,
      matchDuration: matchDuration,
      isNetworkVideo: isNetworkVideo,
      fileSize: fileSize,
      fileHash: fileHash,
    );
  }

  /// 创建失败结果
  factory DanmakuMatchResult.failure({
    required String errorMessage,
    required Duration matchDuration,
    required bool isNetworkVideo,
    int? fileSize,
    String? fileHash,
  }) {
    return DanmakuMatchResult(
      errorMessage: errorMessage,
      danmakuCount: 0,
      matchDuration: matchDuration,
      isNetworkVideo: isNetworkVideo,
      fileSize: fileSize,
      fileHash: fileHash,
    );
  }

  /// 是否成功
  bool get isSuccess => errorMessage == null && episodeName != null;

  /// 获取显示文本
  String get displayText {
    if (isSuccess) {
      final confidenceText = confidence != null 
          ? ' (${(confidence! * 100).toStringAsFixed(1)}%)'
          : '';
      return '$episodeName$confidenceText - $danmakuCount条弹幕';
    } else {
      return errorMessage ?? '未知错误';
    }
  }

  /// 获取详细信息
  String get detailText {
    final buffer = StringBuffer();
    
    if (isSuccess) {
      buffer.writeln('匹配成功！');
      buffer.writeln('剧集：$episodeName');
      if (confidence != null) {
        buffer.writeln('匹配度：${(confidence! * 100).toStringAsFixed(1)}%');
      }
      buffer.writeln('弹幕数量：$danmakuCount条');
    } else {
      buffer.writeln('匹配失败');
      buffer.writeln('错误：$errorMessage');
    }
    
    buffer.writeln('耗时：${matchDuration.inMilliseconds}ms');
    buffer.writeln('视频类型：${isNetworkVideo ? '网络视频' : '本地文件'}');
    
    if (fileSize != null) {
      final sizeText = fileSize! > 1024 * 1024 
          ? '${(fileSize! / (1024 * 1024)).toStringAsFixed(1)}MB'
          : '${(fileSize! / 1024).toStringAsFixed(1)}KB';
      buffer.writeln('文件大小：$sizeText');
    }
    
    if (fileHash != null) {
      buffer.writeln('文件哈希：${fileHash!.substring(0, 8)}...');
    }
    
    return buffer.toString().trim();
  }
}
