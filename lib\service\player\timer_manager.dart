import 'dart:async';
import 'package:flutter/foundation.dart';

/// 定时器任务类型枚举
enum TimerTaskType {
  /// 历史记录更新（高优先级）
  historyUpdate,
  /// 快照保存（低优先级）
  snapshotSave,
}

/// 定时器任务
class TimerTask {
  final TimerTaskType type;
  final Future<void> Function() execute;
  final String name;
  
  const TimerTask({
    required this.type,
    required this.execute,
    required this.name,
  });
  
  /// 任务优先级（数字越小优先级越高）
  int get priority {
    switch (type) {
      case TimerTaskType.historyUpdate:
        return 1;
      case TimerTaskType.snapshotSave:
        return 2;
    }
  }
}

/// 播放器定时器管理器
/// 统一管理所有定时器任务，实现智能调度和资源优化
class PlayerTimerManager {
  Timer? _mainTimer;
  final List<TimerTask> _tasks = [];
  bool _isPlaying = false;
  bool _isDisposed = false;
  
  /// 当前调度间隔
  Duration get _currentInterval {
    // 播放时3秒间隔，暂停时30秒间隔
    return _isPlaying ? const Duration(seconds: 3) : const Duration(seconds: 30);
  }
  
  /// 添加定时器任务
  void scheduleTask(TimerTask task) {
    if (_isDisposed) return;
    
    // 检查是否已存在相同类型的任务
    _tasks.removeWhere((existingTask) => existingTask.type == task.type);
    
    // 添加新任务
    _tasks.add(task);
    
    // 按优先级排序
    _tasks.sort((a, b) => a.priority.compareTo(b.priority));
    
    debugPrint('PlayerTimerManager: 添加任务 ${task.name}，当前任务数: ${_tasks.length}');
    
    // 重新调度
    _reschedule();
  }
  
  /// 移除指定类型的任务
  void removeTask(TimerTaskType type) {
    if (_isDisposed) return;
    
    final removedCount = _tasks.length;
    _tasks.removeWhere((task) => task.type == type);
    final currentCount = _tasks.length;
    
    if (removedCount != currentCount) {
      debugPrint('PlayerTimerManager: 移除任务类型 $type');
      _reschedule();
    }
  }
  
  /// 更新播放状态
  void updatePlayingState(bool isPlaying) {
    if (_isDisposed || _isPlaying == isPlaying) return;
    
    _isPlaying = isPlaying;
    debugPrint('PlayerTimerManager: 播放状态变更为 ${isPlaying ? "播放" : "暂停"}，调整调度间隔为 ${_currentInterval.inSeconds}秒');
    
    // 重新调度以应用新的间隔
    _reschedule();
  }
  
  /// 重新调度定时器
  void _reschedule() {
    if (_isDisposed) return;
    
    // 取消现有定时器
    _mainTimer?.cancel();
    _mainTimer = null;
    
    // 如果没有任务，不启动定时器
    if (_tasks.isEmpty) {
      debugPrint('PlayerTimerManager: 无任务，停止定时器');
      return;
    }
    
    // 启动新的定时器
    _mainTimer = Timer.periodic(_currentInterval, _executeTasks);
    debugPrint('PlayerTimerManager: 重新调度定时器，间隔: ${_currentInterval.inSeconds}秒，任务数: ${_tasks.length}');
  }
  
  /// 执行所有任务
  void _executeTasks(Timer timer) async {
    if (_isDisposed || _tasks.isEmpty) return;
    
    debugPrint('PlayerTimerManager: 开始执行 ${_tasks.length} 个任务');
    
    // 按优先级顺序执行任务
    for (final task in _tasks) {
      if (_isDisposed) break;
      
      try {
        await task.execute();
        debugPrint('PlayerTimerManager: 任务 ${task.name} 执行成功');
      } catch (e) {
        debugPrint('PlayerTimerManager: 任务 ${task.name} 执行失败: $e');
        // 继续执行其他任务，不因单个任务失败而中断
      }
    }
  }
  
  /// 立即执行所有任务（用于手动触发）
  Future<void> executeTasksImmediately() async {
    if (_isDisposed || _tasks.isEmpty) return;
    
    debugPrint('PlayerTimerManager: 立即执行所有任务');
    await _executeTasks(_mainTimer!);
  }
  
  /// 获取当前任务数量
  int get taskCount => _tasks.length;
  
  /// 获取当前调度间隔（秒）
  int get currentIntervalSeconds => _currentInterval.inSeconds;
  
  /// 是否正在运行
  bool get isRunning => _mainTimer?.isActive == true;
  
  /// 释放资源
  void dispose() {
    if (_isDisposed) return;
    
    debugPrint('PlayerTimerManager: 开始释放资源');
    
    _isDisposed = true;
    _mainTimer?.cancel();
    _mainTimer = null;
    _tasks.clear();
    
    debugPrint('PlayerTimerManager: 资源释放完成');
  }
}
