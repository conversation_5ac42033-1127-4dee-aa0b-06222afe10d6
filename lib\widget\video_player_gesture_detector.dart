import 'package:flutter/material.dart';

/// 视频播放器手势检测器
/// 处理各种手势操作：单击、双击、长按、滑动等
class VideoPlayerGestureDetector extends StatefulWidget {
  final Widget child;
  final VoidCallback? onSingleTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPressStart;
  final VoidCallback? onLongPressEnd;
  final Function(double)? onVerticalDragLeft; // 左侧滑动调整亮度
  final Function(double)? onVerticalDragRight; // 右侧滑动调整音量
  final Function(double)? onHorizontalDrag; // 水平滑动调整进度
  final VoidCallback? onPanStart;
  final VoidCallback? onPanEnd;

  const VideoPlayerGestureDetector({
    super.key,
    required this.child,
    this.onSingleTap,
    this.onDoubleTap,
    this.onLongPressStart,
    this.onLongPressEnd,
    this.onVerticalDragLeft,
    this.onVerticalDragRight,
    this.onHorizontalDrag,
    this.onPanStart,
    this.onPanEnd,
  });

  @override
  State<VideoPlayerGestureDetector> createState() =>
      _VideoPlayerGestureDetectorState();
}

enum _GestureType { none, horizontal, verticalLeft, verticalRight }

class _VideoPlayerGestureDetectorState
    extends State<VideoPlayerGestureDetector> {
  Offset? _dragStartPosition;
  _GestureType _lockedGesture = _GestureType.none;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onSingleTap,
      onDoubleTap: widget.onDoubleTap,
      onLongPressStart: (details) {
        widget.onLongPressStart?.call();
      },
      onLongPressEnd: (details) {
        widget.onLongPressEnd?.call();
      },
      onPanStart: (details) {
        _dragStartPosition = details.localPosition;
        _lockedGesture = _GestureType.none;
        widget.onPanStart?.call();
      },
      onPanUpdate: (details) {
        if (_dragStartPosition == null) return;

        final screenSize = MediaQuery.of(context).size;
        final deltaX = details.localPosition.dx - _dragStartPosition!.dx;
        final deltaY = details.localPosition.dy - _dragStartPosition!.dy;

        if (_lockedGesture == _GestureType.none) {
          // 首次更新时锁定手势
          if (deltaX.abs() > deltaY.abs()) {
            _lockedGesture = _GestureType.horizontal;
          } else {
            if (_dragStartPosition!.dx < screenSize.width / 2) {
              _lockedGesture = _GestureType.verticalLeft;
            } else {
              _lockedGesture = _GestureType.verticalRight;
            }
          }
        }

        // 根据锁定的手势处理事件
        switch (_lockedGesture) {
          case _GestureType.horizontal:
            if (widget.onHorizontalDrag != null) {
              // 直接传递原始增量
              widget.onHorizontalDrag!(deltaX);
            }
            break;
          case _GestureType.verticalLeft:
            if (widget.onVerticalDragLeft != null) {
              // 直接传递原始增量, Y轴方向相反
              widget.onVerticalDragLeft!(-deltaY);
            }
            break;
          case _GestureType.verticalRight:
            if (widget.onVerticalDragRight != null) {
              // 直接传递原始增量, Y轴方向相反
              widget.onVerticalDragRight!(-deltaY);
            }
            break;
          case _GestureType.none:
            // Do nothing
            break;
        }
      },
      onPanEnd: (details) {
        _dragStartPosition = null;
        _lockedGesture = _GestureType.none;
        widget.onPanEnd?.call();
      },
      child: widget.child,
    );
  }
}

/// 手势操作类型
enum GestureType {
  singleTap,
  doubleTap,
  longPress,
  verticalDragLeft,
  verticalDragRight,
  horizontalDrag,
}

/// 手势操作数据
class GestureData {
  final GestureType type;
  final double? value;
  final Offset? position;

  const GestureData({required this.type, this.value, this.position});
}

/// 手势操作回调
typedef GestureCallback = void Function(GestureData data);
