import 'package:flutter/material.dart';
import 'package:flutter_volume_controller/flutter_volume_controller.dart';

/// 音量控制组件
/// 显示当前音量并提供调节功能
class VolumeControlWidget extends StatefulWidget {
  final double volume;
  final bool isVisible;
  final Duration visibilityDuration;

  const VolumeControlWidget({
    super.key,
    required this.volume,
    required this.isVisible,
    this.visibilityDuration = const Duration(seconds: 2),
  });

  @override
  State<VolumeControlWidget> createState() => _VolumeControlWidgetState();
}

class _VolumeControlWidgetState extends State<VolumeControlWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(VolumeControlWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: _buildVolumeIndicator(),
        );
      },
    );
  }

  Widget _buildVolumeIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getVolumeIcon(), color: Colors.white, size: 32),
          const SizedBox(height: 8),
          Text(
            '${(widget.volume * 100).round()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildVolumeBar(),
        ],
      ),
    );
  }

  Widget _buildVolumeBar() {
    return Container(
      width: 4,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          width: 4,
          height: 100 * widget.volume,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }

  IconData _getVolumeIcon() {
    if (widget.volume == 0) {
      return Icons.volume_off;
    } else if (widget.volume < 0.5) {
      return Icons.volume_down;
    } else {
      return Icons.volume_up;
    }
  }
}

/// 音量控制服务
class VolumeControlService {
  static double _currentVolume = 0.5;
  static final List<VoidCallback> _listeners = [];

  /// 获取当前音量
  static double get currentVolume => _currentVolume;

  /// 设置音量
  static Future<void> setVolume(double volume) async {
    volume = volume.clamp(0.0, 1.0);
    _currentVolume = volume;

    try {
      await FlutterVolumeController.setVolume(volume);
      _notifyListeners();
    } catch (e) {
      debugPrint('设置音量失败: $e');
    }
  }

  /// 同步获取当前音量值
  static Future<double> getVolume() async {
    try {
      _currentVolume =
          await FlutterVolumeController.getVolume() ?? _currentVolume;
      return _currentVolume;
    } catch (e) {
      debugPrint('获取音量失败: $e');
      return _currentVolume;
    }
  }

  /// 增加音量
  static Future<void> increaseVolume([double delta = 0.1]) async {
    await setVolume(_currentVolume + delta);
  }

  /// 减少音量
  static Future<void> decreaseVolume([double delta = 0.1]) async {
    await setVolume(_currentVolume - delta);
  }

  /// 静音/取消静音
  static Future<void> toggleMute() async {
    if (_currentVolume > 0) {
      await setVolume(0);
    } else {
      await setVolume(0.5);
    }
  }

  /// 初始化音量服务
  static Future<void> initialize() async {
    try {
      _currentVolume = await FlutterVolumeController.getVolume() ?? 0.5;

      // 监听系统音量变化
      FlutterVolumeController.addListener((volume) {
        _currentVolume = volume;
        _notifyListeners();
      });
    } catch (e) {
      debugPrint('初始化音量服务失败: $e');
    }
  }

  /// 添加监听器
  static void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 移除监听器
  static void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 通知监听器
  static void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// 释放资源
  static void dispose() {
    _listeners.clear();
    FlutterVolumeController.removeListener();
  }
}
