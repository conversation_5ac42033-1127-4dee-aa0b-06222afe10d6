import 'package:flutter/material.dart';

/// 播放速度控制面板
class PlaybackSpeedPanel extends StatefulWidget {
  final double currentSpeed;
  final Function(double) onSpeedChanged;
  final VoidCallback? onClose;

  const PlaybackSpeedPanel({
    super.key,
    required this.currentSpeed,
    required this.onSpeedChanged,
    this.onClose,
  });

  @override
  State<PlaybackSpeedPanel> createState() => _PlaybackSpeedPanelState();
}

class _PlaybackSpeedPanelState extends State<PlaybackSpeedPanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  static const List<double> speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: _buildPanel(),
          ),
        );
      },
    );
  }

  Widget _buildPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildSpeedOptions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.speed, color: Colors.white, size: 20),
        const SizedBox(width: 8),
        const Text(
          '播放速度',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 16),
        if (widget.onClose != null)
          GestureDetector(
            onTap: widget.onClose,
            child: const Icon(Icons.close, color: Colors.white70, size: 20),
          ),
      ],
    );
  }

  Widget _buildSpeedOptions() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: speeds.map((speed) => _buildSpeedButton(speed)).toList(),
    );
  }

  Widget _buildSpeedButton(double speed) {
    final isSelected = (speed - widget.currentSpeed).abs() < 0.01;

    return GestureDetector(
      onTap: () => widget.onSpeedChanged(speed),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected ? Colors.white : Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected ? Colors.white : Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Text(
          '${speed}x',
          style: TextStyle(
            color: isSelected ? Colors.black : Colors.white,
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}

/// 播放速度快捷操作组件
class PlaybackSpeedIndicator extends StatefulWidget {
  final double speed;
  final bool isVisible;
  final Duration visibilityDuration;

  const PlaybackSpeedIndicator({
    super.key,
    required this.speed,
    required this.isVisible,
    this.visibilityDuration = const Duration(seconds: 2),
  });

  @override
  State<PlaybackSpeedIndicator> createState() => _PlaybackSpeedIndicatorState();
}

class _PlaybackSpeedIndicatorState extends State<PlaybackSpeedIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(PlaybackSpeedIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(opacity: _fadeAnimation.value, child: _buildIndicator());
      },
    );
  }

  Widget _buildIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.speed, color: Colors.white, size: 16),
          const SizedBox(width: 4),
          Text(
            '${widget.speed}x',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
