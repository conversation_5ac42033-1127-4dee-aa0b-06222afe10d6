import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signals/signals.dart';
import '../../widget/player_notification_widget.dart';

/// 播放器UI状态管理类
/// 统一管理所有UI相关的状态，使用Signal替代setState
class PlayerUIState {
  // 控制栏显示状态
  final Signal<bool> _showControls = Signal(true);
  Timer? _hideControlsTimer;

  // 手势控制状态
  final Signal<bool> _isGesturing = Signal(false);
  final Signal<bool> _showVolumeControl = Signal(false);
  final Signal<bool> _showBrightnessControl = Signal(false);
  final Signal<bool> _showProgressIndicator = Signal(false);
  final Signal<bool> _showSpeedPanel = Signal(false);
  final Signal<bool> _showSpeedIndicator = Signal(false);
  final Signal<bool> _showDanmakuSettings = Signal(false);

  // 当前控制值
  final Signal<double> _currentVolume = Signal(0.5);
  final Signal<double> _currentBrightness = Signal(0.5);
  final Signal<String> _progressIndicatorText = Signal("");

  // 手势拖动初始值
  double? _initialVolumeOnPan;
  double? _initialBrightnessOnPan;
  Duration? _initialPositionOnPan;

  // 通知状态
  final Signal<PlayerNotification?> _currentNotification = Signal(null);

  // 只读访问器
  ReadonlySignal<bool> get showControls => _showControls.readonly();
  ReadonlySignal<bool> get isGesturing => _isGesturing.readonly();
  ReadonlySignal<bool> get showVolumeControl => _showVolumeControl.readonly();
  ReadonlySignal<bool> get showBrightnessControl => _showBrightnessControl.readonly();
  ReadonlySignal<bool> get showProgressIndicator => _showProgressIndicator.readonly();
  ReadonlySignal<bool> get showSpeedPanel => _showSpeedPanel.readonly();
  ReadonlySignal<bool> get showSpeedIndicator => _showSpeedIndicator.readonly();
  ReadonlySignal<bool> get showDanmakuSettings => _showDanmakuSettings.readonly();
  ReadonlySignal<double> get currentVolume => _currentVolume.readonly();
  ReadonlySignal<double> get currentBrightness => _currentBrightness.readonly();
  ReadonlySignal<String> get progressIndicatorText => _progressIndicatorText.readonly();
  ReadonlySignal<PlayerNotification?> get currentNotification => _currentNotification.readonly();

  // 手势拖动初始值访问器
  double? get initialVolumeOnPan => _initialVolumeOnPan;
  double? get initialBrightnessOnPan => _initialBrightnessOnPan;
  Duration? get initialPositionOnPan => _initialPositionOnPan;

  /// 显示控制栏并设置自动隐藏
  void showControlsTemporarily() {
    _showControls.value = true;
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (!_isGesturing.value) {
        _showControls.value = false;
      }
    });
  }

  /// 更新控制栏显示状态
  void updateControlsVisibility(bool show) {
    _showControls.value = show;
  }

  /// 开始手势操作
  void startGesture({
    double? initialVolume,
    double? initialBrightness,
    Duration? initialPosition,
  }) {
    // 记录初始值
    _initialVolumeOnPan = initialVolume;
    _initialBrightnessOnPan = initialBrightness;
    _initialPositionOnPan = initialPosition;

    // 批量更新状态
    batch(() {
      _isGesturing.value = true;
      _showControls.value = false;
    });
  }

  /// 结束手势操作
  void endGesture() {
    batch(() {
      _isGesturing.value = false;
      _showBrightnessControl.value = false;
      _showProgressIndicator.value = false;
      _showVolumeControl.value = false;
    });

    // 清除初始值
    _initialVolumeOnPan = null;
    _initialBrightnessOnPan = null;
    _initialPositionOnPan = null;
  }

  /// 开始长按（倍速）
  void startLongPress() {
    batch(() {
      _showSpeedIndicator.value = true;
      _isGesturing.value = true;
      _showControls.value = false;
    });
  }

  /// 结束长按（倍速）
  void endLongPress() {
    batch(() {
      _showSpeedIndicator.value = false;
      _isGesturing.value = false;
    });
  }

  /// 显示音量控制
  void showVolumeControl(double volume) {
    batch(() {
      _currentVolume.value = volume;
      _showVolumeControl.value = true;
      _showBrightnessControl.value = false;
      _showProgressIndicator.value = false;
    });
  }

  /// 显示亮度控制
  void showBrightnessControl(double brightness) {
    batch(() {
      _currentBrightness.value = brightness;
      _showBrightnessControl.value = true;
      _showVolumeControl.value = false;
      _showProgressIndicator.value = false;
    });
  }

  /// 显示进度指示器
  void showProgressIndicator(String text) {
    batch(() {
      _progressIndicatorText.value = text;
      _showProgressIndicator.value = true;
      _showVolumeControl.value = false;
      _showBrightnessControl.value = false;
    });
  }

  /// 隐藏所有控制指示器
  void hideAllIndicators() {
    batch(() {
      _showVolumeControl.value = false;
      _showBrightnessControl.value = false;
      _showProgressIndicator.value = false;
    });
  }

  /// 显示速度面板
  void showSpeedPanel() {
    _showSpeedPanel.value = true;
  }

  /// 隐藏速度面板
  void hideSpeedPanel() {
    _showSpeedPanel.value = false;
  }

  /// 显示弹幕设置
  void showDanmakuSettings() {
    _showDanmakuSettings.value = true;
  }

  /// 隐藏弹幕设置
  void hideDanmakuSettings() {
    _showDanmakuSettings.value = false;
  }

  /// 更新通知状态
  void updateNotification(PlayerNotification? notification) {
    _currentNotification.value = notification;
  }

  /// 释放资源
  void dispose() {
    debugPrint('PlayerUIState: 开始释放资源');
    
    _hideControlsTimer?.cancel();
    _hideControlsTimer = null;

    // 释放所有信号
    _showControls.dispose();
    _isGesturing.dispose();
    _showVolumeControl.dispose();
    _showBrightnessControl.dispose();
    _showProgressIndicator.dispose();
    _showSpeedPanel.dispose();
    _showSpeedIndicator.dispose();
    _showDanmakuSettings.dispose();
    _currentVolume.dispose();
    _currentBrightness.dispose();
    _progressIndicatorText.dispose();
    _currentNotification.dispose();

    debugPrint('PlayerUIState: 资源释放完成');
  }
}
