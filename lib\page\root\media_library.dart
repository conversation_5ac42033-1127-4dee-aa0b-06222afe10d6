import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:dandanplay_flutter/service/media_library.dart';

class MediaLibraryPage extends StatelessWidget {
  const MediaLibraryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();

    return Watch((_) {
      final libraries = mediaLibraryService.mediaLibraries.value;

      return FTileGroup(
        divider: FTileDivider.indented,
        style: tileGroupStyle(
          colors: context.theme.colors,
          typography: context.theme.typography.copyWith(
            base: context.theme.typography.xl,
          ),
          style: context.theme.style,
          newColors: context.theme.colors.copyWith(
            border: Color.fromARGB(0, 238, 238, 238),
          ),
        ),
        children:
            libraries
                .map(
                  (library) => FTile(
                    prefixIcon: const Icon(FIcons.folder),
                    title: Text(library.name),
                    subtitle: Text(library.url),
                    onPress: () {
                      context.push('$fileExplorerPath?id=${library.id}');
                    },
                    onLongPress: () async {
                      showAdaptiveDialog(
                        context: context,
                        builder:
                            (context) => FDialog(
                              direction: Axis.vertical,
                              actions: [
                                FButton(
                                  child: const Text('编辑'),
                                  onPress: () {
                                    Navigator.pop(context);
                                    context.push(
                                      '$editMediaLibraryPath?id=${library.id}',
                                    );
                                  },
                                ),
                                FButton(
                                  style: context.theme.buttonStyles.destructive,
                                  onPress: () {
                                    Navigator.pop(context);
                                    showAdaptiveDialog(
                                      context: context,
                                      builder:
                                          (context) => FDialog(
                                            direction: Axis.vertical,
                                            title: Text('删除媒体库'),
                                            body: Text(
                                              '确定要删除媒体库 "${library.name}" 吗？',
                                            ),
                                            actions: [
                                              FButton(
                                                onPress:
                                                    () =>
                                                        Navigator.pop(context),
                                                child: const Text('取消'),
                                              ),
                                              FButton(
                                                style:
                                                    context
                                                        .theme
                                                        .buttonStyles
                                                        .destructive,
                                                onPress: () {
                                                  Navigator.pop(context);
                                                  mediaLibraryService
                                                      .deleteMediaLibrary(
                                                        library.id,
                                                      );
                                                },
                                                child: const Text('删除'),
                                              ),
                                            ],
                                          ),
                                    );
                                  },
                                  child: const Text('删除'),
                                ),
                              ],
                            ),
                      );
                    },
                  ),
                )
                .toList(),
      );
    });
  }
}
