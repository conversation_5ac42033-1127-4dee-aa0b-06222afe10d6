import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:drift/drift.dart' hide Column;

class EditMediaLibraryPage extends StatefulWidget {
  final int? id;

  const EditMediaLibraryPage({super.key, this.id});

  @override
  State<EditMediaLibraryPage> createState() => _EditMediaLibraryPageState();
}

class _EditMediaLibraryPageState extends State<EditMediaLibraryPage> {
  final _formKey = GlobalKey<FormState>();
  var _mediaLibrary = MediaLibraryExtension.create();
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();
  final _accountController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadMediaLibrary();
  }

  Future<void> _loadMediaLibrary() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();

    if (widget.id != null) {
      final result = await mediaLibraryService.getMediaLibrary(widget.id!);
      if (result != null) {
        _mediaLibrary = result;
      }
    }

    setState(() {
      _nameController.text = _mediaLibrary.name;
      _urlController.text = _mediaLibrary.url;
      _accountController.text = _mediaLibrary.account ?? '';
      _passwordController.text = _mediaLibrary.password ?? '';
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<bool> _saveMediaLibrary(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });

    final nameError = _validateRequired(_nameController.text, '名称');
    final urlError = _validateUrl(_urlController.text);
    final accountError = _validateRequired(_accountController.text, '账号');
    final passwordError = _validateRequired(_passwordController.text, '密码');

    if (nameError != null) {
      setState(() {
        _isLoading = false;
        _errorMessage = nameError;
      });
      return false;
    }

    if (urlError != null) {
      setState(() {
        _isLoading = false;
        _errorMessage = urlError;
      });
      return false;
    }

    if (accountError != null && !_mediaLibrary.isAnonymous) {
      setState(() {
        _isLoading = false;
        _errorMessage = accountError;
      });
      return false;
    }

    if (passwordError != null && !_mediaLibrary.isAnonymous) {
      setState(() {
        _isLoading = false;
        _errorMessage = passwordError;
      });
      return false;
    }

    final updatedLibrary = _mediaLibrary.copyWith(
      name: _nameController.text.trim(),
      url: _urlController.text.trim(),
      account:
          _accountController.text.trim().isEmpty
              ? const Value.absent()
              : Value(_accountController.text.trim()),
      password:
          _passwordController.text.isEmpty
              ? const Value.absent()
              : Value(_passwordController.text),
    );

    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    await mediaLibraryService.updateMediaLibrary(updatedLibrary);

    _nameController.clear();
    _urlController.clear();
    _accountController.clear();
    _passwordController.clear();
    setState(() {
      _isLoading = false;
    });
    return true;
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName不能为空';
    }
    return null;
  }

  String? _validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'URL不能为空';
    }

    final uri = Uri.tryParse(value.trim());
    if (uri == null || !uri.hasScheme) {
      return '请输入有效的URL';
    }

    return null;
  }

  void showtoast() {
    showFToast(
      context: context,
      title: const Text('保存失败'),
      description: Text(_errorMessage),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: FHeader(title: const Text('编辑媒体库')),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            FTextField(label: const Text('名称'), controller: _nameController),
            const SizedBox(height: 10),
            FTextField(label: const Text('URL'), controller: _urlController),
            const SizedBox(height: 10),
            FTextField(label: const Text('账号'), controller: _accountController),
            const SizedBox(height: 10),
            FTextField(
              label: const Text('密码'),
              obscureText: true,
              controller: _passwordController,
            ),
            const SizedBox(height: 10),
            FSwitch(
              label: const Text('匿名访问'),
              value: _mediaLibrary.isAnonymous,
              onChange: (value) {
                setState(() {
                  _mediaLibrary = _mediaLibrary.copyWith(isAnonymous: value);
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: FButton(
                    style: context.theme.buttonStyles.secondary,
                    onPress: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: FButton(
                    onPress:
                        _isLoading
                            ? null
                            : () async {
                              var result = await _saveMediaLibrary(context);
                              if (!result) {
                                showtoast();
                              }
                              if (context.mounted && result) {
                                Navigator.of(context).pop();
                              }
                            },
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('保存'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
