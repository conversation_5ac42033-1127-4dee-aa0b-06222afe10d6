import 'package:flutter/material.dart';

/// 弹幕类型
enum DanmakuType {
  /// 滚动弹幕
  scroll,
  /// 顶部弹幕
  top,
  /// 底部弹幕
  bottom,
}

/// 弹幕数据模型
class Danmaku {
  /// 弹幕文本
  final String text;
  /// 出现时间
  final Duration time;
  /// 弹幕类型
  final DanmakuType type;
  /// 弹幕颜色
  final Color color;
  /// 字体大小
  final double fontSize;
  /// 弹幕ID
  final String? id;

  const <PERSON><PERSON><PERSON>({
    required this.text,
    required this.time,
    this.type = DanmakuType.scroll,
    this.color = Colors.white,
    this.fontSize = 16.0,
    this.id,
  });

  /// 从JSON创建弹幕
  factory Danmaku.fromJson(Map<String, dynamic> json) {
    return Danmaku(
      text: json['text'] as String,
      time: Duration(milliseconds: (json['time'] as num).toInt()),
      type: DanmakuType.values[json['type'] as int? ?? 0],
      color: Color(json['color'] as int? ?? 0xFFFFFFFF),
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      id: json['id'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'time': time.inMilliseconds,
      'type': type.index,
      'color': color.value,
      'fontSize': fontSize,
      if (id != null) 'id': id,
    };
  }

  @override
  String toString() {
    return 'Danmaku(text: $text, time: $time, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Danmaku &&
        other.text == text &&
        other.time == time &&
        other.type == type &&
        other.color == color &&
        other.fontSize == fontSize &&
        other.id == id;
  }

  @override
  int get hashCode {
    return Object.hash(text, time, type, color, fontSize, id);
  }
}

/// 弹幕设置
class DanmakuSettings {
  /// 是否启用弹幕
  final bool enabled;
  /// 弹幕透明度 (0.0 - 1.0)
  final double opacity;
  /// 字体大小倍数 (0.5 - 2.0)
  final double fontSizeScale;
  /// 弹幕速度倍数 (0.5 - 2.0)
  final double speedScale;
  /// 是否显示顶部弹幕
  final bool showTop;
  /// 是否显示底部弹幕
  final bool showBottom;
  /// 是否显示滚动弹幕
  final bool showScroll;
  /// 弹幕密度 (0.1 - 1.0)
  final double density;
  /// 字体粗细
  final FontWeight fontWeight;

  const DanmakuSettings({
    this.enabled = true,
    this.opacity = 0.8,
    this.fontSizeScale = 1.0,
    this.speedScale = 1.0,
    this.showTop = true,
    this.showBottom = true,
    this.showScroll = true,
    this.density = 1.0,
    this.fontWeight = FontWeight.normal,
  });

  /// 从JSON创建设置
  factory DanmakuSettings.fromJson(Map<String, dynamic> json) {
    return DanmakuSettings(
      enabled: json['enabled'] as bool? ?? true,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 0.8,
      fontSizeScale: (json['fontSizeScale'] as num?)?.toDouble() ?? 1.0,
      speedScale: (json['speedScale'] as num?)?.toDouble() ?? 1.0,
      showTop: json['showTop'] as bool? ?? true,
      showBottom: json['showBottom'] as bool? ?? true,
      showScroll: json['showScroll'] as bool? ?? true,
      density: (json['density'] as num?)?.toDouble() ?? 1.0,
      fontWeight: FontWeight.values[json['fontWeight'] as int? ?? FontWeight.normal.index],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'opacity': opacity,
      'fontSizeScale': fontSizeScale,
      'speedScale': speedScale,
      'showTop': showTop,
      'showBottom': showBottom,
      'showScroll': showScroll,
      'density': density,
      'fontWeight': fontWeight.index,
    };
  }

  /// 复制并修改设置
  DanmakuSettings copyWith({
    bool? enabled,
    double? opacity,
    double? fontSizeScale,
    double? speedScale,
    bool? showTop,
    bool? showBottom,
    bool? showScroll,
    double? density,
    FontWeight? fontWeight,
  }) {
    return DanmakuSettings(
      enabled: enabled ?? this.enabled,
      opacity: opacity ?? this.opacity,
      fontSizeScale: fontSizeScale ?? this.fontSizeScale,
      speedScale: speedScale ?? this.speedScale,
      showTop: showTop ?? this.showTop,
      showBottom: showBottom ?? this.showBottom,
      showScroll: showScroll ?? this.showScroll,
      density: density ?? this.density,
      fontWeight: fontWeight ?? this.fontWeight,
    );
  }
}

/// 弹弹play API 节目信息
class Episode {
  /// 节目ID
  final String episodeId;
  /// 动画名称
  final String animeTitle;
  /// 节目标题
  final String episodeTitle;
  /// 节目类型
  final String type;
  /// 匹配度
  final double shift;

  const Episode({
    required this.episodeId,
    required this.animeTitle,
    required this.episodeTitle,
    required this.type,
    required this.shift,
  });

  /// 从JSON创建节目信息
  factory Episode.fromJson(Map<String, dynamic> json) {
    return Episode(
      episodeId: json['episodeId'].toString(),
      animeTitle: json['animeTitle'] as String,
      episodeTitle: json['episodeTitle'] as String,
      type: json['type'] as String,
      shift: (json['shift'] as num).toDouble(),
    );
  }

  @override
  String toString() {
    return 'Episode(episodeId: $episodeId, animeTitle: $animeTitle, episodeTitle: $episodeTitle)';
  }
}

/// 弹弹play API 弹幕评论
class DanmakuComment {
  /// 弹幕ID
  final int cid;
  /// 出现时间（秒）
  final double p;
  /// 弹幕内容
  final String m;

  const DanmakuComment({
    required this.cid,
    required this.p,
    required this.m,
  });

  /// 从JSON创建弹幕评论
  factory DanmakuComment.fromJson(Map<String, dynamic> json) {
    return DanmakuComment(
      cid: json['cid'] as int,
      p: (json['p'] as num).toDouble(),
      m: json['m'] as String,
    );
  }

  /// 转换为Danmaku对象
  Danmaku toDanmaku() {
    return Danmaku(
      text: m,
      time: Duration(milliseconds: (p * 1000).toInt()),
      id: cid.toString(),
    );
  }
}
