import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../model/danmaku.dart';

/// 弹幕设置面板
class DanmakuSettingsPanel extends StatefulWidget {
  final DanmakuSettings settings;
  final Function(DanmakuSettings) onSettingsChanged;
  final VoidCallback? onClose;
  final ScrollController? scrollController;

  const DanmakuSettingsPanel({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
    this.onClose,
    this.scrollController,
  });

  @override
  State<DanmakuSettingsPanel> createState() => _DanmakuSettingsPanelState();
}

class _DanmakuSettingsPanelState extends State<DanmakuSettingsPanel> {
  late DanmakuSettings _currentSettings;

  @override
  void initState() {
    super.initState();
    _currentSettings = widget.settings;
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      controller: widget.scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      children: [
        _buildHeader(),
        const SizedBox(height: 20),
        _buildSettings(),
        const SizedBox(height: 20),
        _buildButtons(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.subtitles, color: Colors.white, size: 20),
        const SizedBox(width: 8),
        const Text(
          '弹幕设置',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (widget.onClose != null)
          GestureDetector(
            onTap: widget.onClose,
            child: const Icon(Icons.close, color: Colors.white70, size: 20),
          ),
      ],
    );
  }

  Widget _buildSettings() {
    return Column(
      children: [
        // 弹幕开关
        _buildSwitchSetting('启用弹幕', _currentSettings.enabled, (value) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(enabled: value);
          });
        }),
        const SizedBox(height: 16),

        // 透明度设置
        _buildSliderSetting('透明度', _currentSettings.opacity, 0.1, 1.0, (value) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(opacity: value);
          });
        }),
        const SizedBox(height: 16),

        // 字体大小设置
        _buildSliderSetting('字体大小', _currentSettings.fontSizeScale, 0.5, 2.0, (
          value,
        ) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(fontSizeScale: value);
          });
        }),
        const SizedBox(height: 16),

        // 弹幕速度设置
        _buildSliderSetting('弹幕速度', _currentSettings.speedScale, 0.5, 2.0, (
          value,
        ) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(speedScale: value);
          });
        }),
        const SizedBox(height: 16),

        // 弹幕类型开关
        _buildSwitchSetting('显示滚动弹幕', _currentSettings.showScroll, (value) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(showScroll: value);
          });
        }),
        const SizedBox(height: 8),
        _buildSwitchSetting('显示顶部弹幕', _currentSettings.showTop, (value) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(showTop: value);
          });
        }),
        const SizedBox(height: 8),
        _buildSwitchSetting('显示底部弹幕', _currentSettings.showBottom, (value) {
          setState(() {
            _currentSettings = _currentSettings.copyWith(showBottom: value);
          });
        }),
      ],
    );
  }

  Widget _buildSwitchSetting(
    String title,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Text(title, style: const TextStyle(color: Colors.white, fontSize: 14)),
        const Spacer(),
        Switch(value: value, onChanged: onChanged, activeColor: Colors.blue),
      ],
    );
  }

  Widget _buildSliderSetting(
    String title,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            const Spacer(),
            Text(
              '${(value * 100).round()}%',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Slider(
          value: value,
          min: min,
          max: max,
          onChanged: onChanged,
          activeColor: Colors.blue,
          inactiveColor: Colors.white30,
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: FButton(
            onPress: widget.onClose,
            style: FButtonStyle.outline,
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: FButton(
            onPress: () {
              widget.onSettingsChanged(_currentSettings);
              widget.onClose?.call();
            },
            child: const Text('确定'),
          ),
        ),
      ],
    );
  }
}
